package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.DictCode;
import com.bzlj.craft.enums.MaterialAttrType;
import com.bzlj.craft.repository.MaterialAttrRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ISysDictItemService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 投入物料转换处理器
 * <p>
 * 负责处理投入物料消息的转换和处理，主要功能包括：
 * 1. 接收投入物料数据消息
 * 2. 解析物料信息并创建物料实体
 * 3. 处理物料属性和任务物料关联关系
 * 4. 支持批量物料数据处理
 * 5. 异步处理物料数据以提高性能
 * </p>
 * <p>
 * 特别适用于特冶感应炉原料消耗实绩的处理场景
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@MessageHandler(messageType = "input_material", desc = "投入物料转化")
public class InputMaterialTransformHandle extends CommonHandler<String> {

    /**
     * 物料服务，用于管理物料相关业务逻辑
     */
    @Autowired
    private IMaterialService materialService;

    /**
     * 系统字典项服务，用于获取字典数据
     */
    @Autowired
    private ISysDictItemService sysDictItemService;

    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 物料属性仓储，用于管理物料属性数据
     */
    @Autowired
    private MaterialAttrRepository materialAttrRepository;

    /**
     * 投入物料转换服务，用于处理投入物料相关业务逻辑
     */
    @Autowired
    private InputMaterialTransformService inputMaterialTransformService;

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;
    /**
     * 转换处理投入物料消息
     * <p>
     * 接收投入物料的JSON消息，调用具体的物料转换处理方法
     * </p>
     *
     * @param s 投入物料的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s, Map<String, Object> handleContext) {
        inputMaterialTransform(s);
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除相关的电报数据
     * </p>
     *
     * @param telegramId 电报ID，如果为空则不执行删除操作
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    /**
     * 投入物料转换处理方法
     * <p>
     * 处理投入物料JSON数据的核心方法，包括以下步骤：
     * 1. 解析JSON数组，提取物料编码和任务编码
     * 2. 查询相关的生产任务和字典数据
     * 3. 构建物料实体和属性映射关系
     * 4. 异步保存物料数据以提高性能
     * </p>
     *
     * @param json 投入物料的JSON字符串（数组格式）
     * @throws RuntimeException 当任务不存在时抛出异常
     */
    public void inputMaterialTransform(String json) {
        ArrayNode jsonNode = (ArrayNode) JsonUtils.toJsonNode(json);
        List<String> materialCodes = new ArrayList<>();
        Set<String> taskCodes = new HashSet<>();

        // 提取物料编码和任务编码
        jsonNode.iterator().forEachRemaining(item -> {
            materialCodes.add(item.get("materialCode").asText());
            taskCodes.add(item.get("taskCode").asText());
        });

        // 查询生产任务
        List<ProductionTask> tasks = surveillanceService.findByTaskCodes(taskCodes);
        if(CollectionUtils.isEmpty(tasks)){
            throw new RuntimeException(String.format("任务不存在；任务号：%s", tasks));
        }

        // 构建任务映射和属性类型映射
        ImmutableMap<String, ProductionTask> taskMap = Maps.uniqueIndex(tasks, ProductionTask::getTaskCode);
        List<SysDictItem> attrType = sysDictItemService.findEntityByDictCode(DictCode.MATERIAL_ATTR_TYPE.getCode());
        ImmutableMap<String, SysDictItem> attrTypeMap = Maps.uniqueIndex(attrType, SysDictItem::getItemCode);

        // 初始化映射容器
        Map<String, List<MaterialAttr>> materialAttrMap = new HashMap<>();
        Map<String, List<Material>> taskMaterialMap = new HashMap<>();
        int[] order = {1};

        // 处理每个物料项
        jsonNode.iterator().forEachRemaining(item -> {
            Material material = buildMaterial( item, attrTypeMap, materialAttrMap);
            ProductionTask productionTask = taskMap.get(item.get("taskCode").asText());
            assert productionTask != null;
            List<Material> materials = taskMaterialMap.get(productionTask.getTaskCode());
            if(Objects.isNull(materials)){
                materials = new ArrayList<>();
            }
            materials.add(material);
            taskMaterialMap.put(productionTask.getTaskCode(), materials);
            order[0]++;
        });
        if (!CollectionUtils.isEmpty(taskMaterialMap)) {
            List<CompletableFuture<List<TaskMaterial>>> futures = taskMaterialMap.keySet().stream()
                    .map(taskCode -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return saveInputMaterial(taskMaterialMap, materialAttrMap, Objects.requireNonNull(taskMap.get(taskCode)));
                        } catch (Exception e) {
                            return null;
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());
        }
    }

    private List<TaskMaterial> saveInputMaterial(Map<String, List<Material>> taskMaterialMap,Map<String, List<MaterialAttr>> materialAttrMap,ProductionTask productionTask){
        List<Material> materials = taskMaterialMap.get(productionTask.getTaskCode());
        List<Material> materialList = materialService.batchInsertEntity(materials);
        if (!CollectionUtils.isEmpty(materialAttrMap)) {
            materialList.forEach(material -> {
                List<MaterialAttr> attrs = materialAttrMap.get(material.getMaterialCode());
                if (Objects.nonNull(attrs)) {
                    attrs.forEach(materialAttr -> {
                        materialAttr.setMaterial(material);
                    });
                }
            });
            materialAttrRepository.saveAll(materialAttrMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        }
        return taskMaterialService.buildTaskMaterialAndSave(materialList, productionTask, false,materialAttrMap);
    }


    private Material buildMaterial(JsonNode materialNode, Map<String, SysDictItem> sysDictItemMap, Map<String, List<MaterialAttr>> materialAttrMap) {
        Material material = new Material();
        material.setMaterialCode(materialNode.get("materialCode").asText());
        material.setMaterialName(materialNode.get("materialName").asText());
        if(Objects.nonNull(materialNode.get("brand"))){
            material.setBrand((materialNode.get("brand").asText()));
        }
        if(Objects.nonNull(materialNode.get("heatNumber"))){
            material.setHeatNumber((materialNode.get("heatNumber").asText()));
        }
        material.setMaterialType("实际");
        List<MaterialAttr> materialAttrs = new ArrayList<>();
        if (Objects.nonNull(materialNode.get("physicalAttr"))) {
            MaterialAttr materialAttr = new MaterialAttr();
            materialAttr.setAttr(JsonUtils.nodeToMap(materialNode.get("physicalAttr")));
            materialAttr.setAttrType(sysDictItemMap.get(MaterialAttrType.physical_attr.getCode()));
            materialAttrs.add(materialAttr);
        }
        if (Objects.nonNull(materialNode.get("specificationAttr"))) {
            MaterialAttr materialAttr = new MaterialAttr();
            materialAttr.setAttr(JsonUtils.nodeToMap(materialNode.get("specificationAttr")));
            materialAttr.setAttrType(sysDictItemMap.get(MaterialAttrType.specification_attr.getCode()));
            materialAttrs.add(materialAttr);
        }
        if(!CollectionUtils.isEmpty(materialAttrs)){
            materialAttrMap.put(material.getMaterialCode(), materialAttrs);
        }
        return material;

    }

}
